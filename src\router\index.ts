import { createRouter, createWebHistory } from 'vue-router'


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/log',
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/log',
      name: 'Log',
      component: () => import('../views/LogView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/server',
      name: 'server',
      component: () => import('../views/ServerView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/setting',
      name: 'Setting',
      component: () => import('../views/SettingView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/msraccount',
      name: 'Account',
      component: () => import('../views/AccountView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/newaccount',
      name: 'NewAccount',
      component: () => import('../views/NewAccountView.vue'),
      meta: { requiresAuth: true }
    },
  ],
})
// 添加路由守卫
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
