import moment from 'moment-timezone';

/**
 * 账号数据接口定义
 */
export interface AccountData {
    email: string;
    password: string;
    proofEmail?: string;
    server?: string;
    score?: number;
    weight?: number;
    disabled?: number;
    lock?: number;
    pcSearchPointProgress?: number;
    mobileSearchPointProgress?: number;
    pcSearchCount?: number;
    mobileSearchCount?: number;
    executions?: number;
    createDatetime?: string;
    updateDatetime?: string;
    maxDailyExecutionLimit?: number;
    maxSearchPerRequest?: number;
    maxDailySearchLimit?: number;
    maxReadPerRequest?: number;
    maxDailyReadLimit?: number;
    ignoreDistributionCycleDays?: number;
    onlyLogin?: number;
}

/**
 * 新账号数据接口定义
 */
export interface NewAccountData {
    email: string;
    password: string;
    proofEmail?: string;
    createDatetime?: string;
}

/**
 * 账号服务类，提供统一的账号数据处理功能
 */
export class AccountService {
    /**
     * 获取当前时间戳
     */
    private static getCurrentTime(): string {
        return moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
    }

    /**
     * 获取账号表的默认值配置
     */
    private static getAccountDefaults(): Partial<AccountData> {
        const currentTime = this.getCurrentTime();
        return {
            proofEmail: '',
            server: '',
            score: 0,
            weight: 0,
            disabled: 0,
            lock: 0,
            pcSearchPointProgress: 0,
            mobileSearchPointProgress: 0,
            pcSearchCount: 0,
            mobileSearchCount: 0,
            executions: 0,
            createDatetime: currentTime,
            updateDatetime: '',
            maxDailyExecutionLimit: -1,
            maxSearchPerRequest: -1,
            maxDailySearchLimit: -1,
            maxReadPerRequest: -1,
            maxDailyReadLimit: -1,
            ignoreDistributionCycleDays: 0,
            onlyLogin: 0
        };
    }

    /**
     * 创建标准的账号数据对象
     * @param sourceAccount 源账号数据
     * @param overrides 需要覆盖的字段
     * @returns 完整的账号数据对象
     */
    static createAccountData(
        sourceAccount: NewAccountData | Partial<AccountData>, 
        overrides: Partial<AccountData> = {}
    ): AccountData {
        const defaults = this.getAccountDefaults();
        
        // 合并数据：默认值 -> 源数据 -> 覆盖值
        const accountData: AccountData = {
            ...defaults,
            ...sourceAccount,
            ...overrides
        } as AccountData;

        // 确保必需字段存在
        if (!accountData.email || !accountData.password) {
            throw new Error('Email and password are required');
        }

        return accountData;
    }

    /**
     * 创建未分配账号数据
     * @param sourceAccount 源账号数据
     * @returns 未分配账号数据
     */
    static createUnassignedAccount(sourceAccount: NewAccountData): AccountData {
        return this.createAccountData(sourceAccount, {
            server: '', // 未分配服务器
            lock: 0     // 未锁定
        });
    }

    /**
     * 创建锁定账号数据
     * @param sourceAccount 源账号数据
     * @returns 锁定账号数据
     */
    static createLockedAccount(sourceAccount: NewAccountData): AccountData {
        return this.createAccountData(sourceAccount, {
            server: '', // 未分配服务器
            lock: 1     // 锁定状态
        });
    }

    /**
     * 为特定服务器创建账号数据
     * @param sourceAccount 源账号数据
     * @param server 服务器ID
     * @returns 服务器账号数据
     */
    static createServerAccount(sourceAccount: Partial<AccountData>, server: string): AccountData {
        return this.createAccountData(sourceAccount, {
            server: server
        });
    }

    /**
     * 批量创建账号数据
     * @param sourceAccounts 源账号数据数组
     * @param createFn 创建函数
     * @returns 账号数据数组
     */
    static createBatchAccounts<T>(
        sourceAccounts: T[], 
        createFn: (account: T) => AccountData
    ): AccountData[] {
        return sourceAccounts.map(account => createFn(account));
    }
}
