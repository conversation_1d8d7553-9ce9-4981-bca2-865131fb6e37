import { type KVNamespace, type D1Database, type Ai } from '@cloudflare/workers-types';


export interface Env {
    ASSETS: {
        fetch: typeof fetch;
    };
    SCHEDULE_INIT_ACCOUNT_SIZE: number;
    SCHEDULE_RESET_ACCOUNT_SIZE: number;
    API_TOKEN: string;    // API 访问令牌
    API_RESET_TOKEN: string;  // 重置账号API认证令牌
    JWT_SECRET: string;   // JWT 密钥
    USER_NAME: string;    // 用户名
    PASSWORD: string;     // 密码
    KV: KVNamespace;
    AI: Ai;
    DB: D1Database;
}

/**
 * 登录凭证接口
 */
export interface LoginCredentials {
    /** 用户名 */
    username: string;
    /** 密码 */
    password: string;
}

/**
 * 系统设置接口
 */
export interface SystemSettings {
    /** 飞书配置 */
    feishu: {
        /** 飞书应用ID */
        app_id: string;
        /** 飞书应用密钥 */
        app_secret: string;
        /** 飞书应用验证Token */
        verification_token: string;
        /** 飞书应用加密Key */
        encrypt_key: string;
        /** 飞书机器人接收ID */
        receive_id: string;
    }
}

