import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq, desc } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const url = new URL(request.url);
        const status = url.searchParams.get('status');

        let accounts;

        // 根据状态筛选并设置排序
        if (status && status !== 'all') {
            switch (status) {
                case 'reset_success':
                    accounts = await db.select()
                        .from(newaccountsTable)
                        .where(eq(newaccountsTable.resetStatus, 1))
                        .orderBy(desc(newaccountsTable.resetDatetime));
                    break;
                case 'reset_failed':
                    accounts = await db.select()
                        .from(newaccountsTable)
                        .where(eq(newaccountsTable.resetStatus, 2))
                        .orderBy(desc(newaccountsTable.resetDatetime));
                    break;
                case 'init_success':
                    accounts = await db.select()
                        .from(newaccountsTable)
                        .where(eq(newaccountsTable.initStatus, 1))
                        .orderBy(desc(newaccountsTable.initDatetime));
                    break;
                case 'init_failed':
                    accounts = await db.select()
                        .from(newaccountsTable)
                        .where(eq(newaccountsTable.initStatus, 2))
                        .orderBy(desc(newaccountsTable.initDatetime));
                    break;
                default:
                    accounts = await db.select().from(newaccountsTable);
                    break;
            }
        } else {
            // 获取所有账号
            accounts = await db.select().from(newaccountsTable);
        }

        return addCorsHeaders(new Response(JSON.stringify(accounts), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};
