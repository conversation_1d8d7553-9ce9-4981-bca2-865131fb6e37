{"version": "6", "dialect": "sqlite", "id": "b38e6cb1-b77d-4b69-8009-83e4bdc425b9", "prevId": "73a1893f-74b3-4301-8f23-7f7e0d688060", "tables": {"accounts": {"name": "accounts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "proofEmail": {"name": "proofEmail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "server": {"name": "server", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "weight": {"name": "weight", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "disabled": {"name": "disabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "lock": {"name": "lock", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "pcSearchPointProgress": {"name": "pcSearchPointProgress", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "mobileSearchPointProgress": {"name": "mobileSearchPointProgress", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "pcSearchCount": {"name": "pcSearchCount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "mobileSearchCount": {"name": "mobileSearchCount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "executions": {"name": "executions", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createDatetime": {"name": "createDatetime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "updateDatetime": {"name": "updateDatetime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "maxDailyExecutionLimit": {"name": "maxDailyExecutionLimit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "maxSearchPerRequest": {"name": "maxSearchPerRequest", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "maxDailySearchLimit": {"name": "maxDailySearchLimit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "maxReadPerRequest": {"name": "maxReadPerRequest", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "maxDailyReadLimit": {"name": "maxDailyReadLimit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "ignoreDistributionCycleDays": {"name": "ignoreDistributionCycleDays", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "onlyLogin": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"server_idx": {"name": "server_idx", "columns": ["server"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "logs": {"name": "logs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "server": {"name": "server", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "service": {"name": "service", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "datetime": {"name": "datetime", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "logs": {"name": "logs", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"logs_compound_idx": {"name": "logs_compound_idx", "columns": ["server", "service", "datetime"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "newaccounts": {"name": "newaccounts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "proofEmail": {"name": "proofEmail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createDatetime": {"name": "createDatetime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "resetStatus": {"name": "resetStatus", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "resetDatetime": {"name": "resetDatetime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "resetFailMsg": {"name": "resetFailMsg", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "initStatus": {"name": "initStatus", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "initDatetime": {"name": "initDatetime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "initFailMsg": {"name": "initFailMsg", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}}, "indexes": {"newaccounts_reset_status_idx": {"name": "newaccounts_reset_status_idx", "columns": ["resetStatus"], "isUnique": false}, "newaccounts_init_status_idx": {"name": "newaccounts_init_status_idx", "columns": ["initStatus"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "servers": {"name": "servers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "disabled": {"name": "disabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "config": {"name": "config", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'{}'"}}, "indexes": {"server_name_idx": {"name": "server_name_idx", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "settings": {"name": "settings", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}