CREATE TABLE `newaccounts` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`email` text NOT NULL,
	`password` text NOT NULL,
	`proofEmail` text,
	`createDatetime` text DEFAULT '',
	`resetStatus` integer DEFAULT 0 NOT NULL,
	`resetDatetime` text DEFAULT '',
	`resetFailMsg` text DEFAULT '',
	`initStatus` integer DEFAULT 0 NOT NULL,
	`initDatetime` text DEFAULT '',
	`initFailMsg` text DEFAULT ''
);
--> statement-breakpoint
CREATE INDEX `newaccounts_reset_status_idx` ON `newaccounts` (`resetStatus`);--> statement-breakpoint
CREATE INDEX `newaccounts_init_status_idx` ON `newaccounts` (`initStatus`);