import { describe, expect, it } from 'vitest';

describe('NewAccount Update API Tests', () => {
  const API_BASE_URL = 'http://localhost:8788';
  const API_TOKEN = '9ba4c11ce3a9235a34b3481795485dec';

  it('should update newaccount records successfully', async () => {
    const testData = [
      {
        "id": 44,
        "email": "<EMAIL>",
        "password": "kqaon1140",
        "proofEmail": "",
        "createDatetime": "2025-07-31 19:06:19",
        "resetStatus": 1, 
        "resetDatetime": "",
        "resetFailMsg": "",
        "initStatus": 0,
        "initDatetime": "",
        "initFailMsg": ""
      },
      {
        "id": 45,
        "email": "<EMAIL>",
        "password": "qeowuvd2605",
        "proofEmail": "",
        "createDatetime": "2025-07-31 19:06:19",
        "resetStatus": 0,
        "resetDatetime": "",
        "resetFailMsg": "",
        "initStatus": 1, 
        "initDatetime": "",
        "initFailMsg": ""
      }
    ];

    const response = await fetch(`${API_BASE_URL}/newaccount/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`
      },
      body: JSON.stringify(testData)
    });

    expect(response.status).toBe(200);
    const data: any = await response.json();
    expect(data.success).toBe(true);
    expect(data.message).toBe('Accounts updated successfully');
    expect(data).toHaveProperty('updatedCount');
    expect(data).toHaveProperty('notFoundCount');
    expect(data).toHaveProperty('notFoundEmails');
    expect(data).toHaveProperty('timestamp');
  });

  it('should handle invalid data format', async () => {
    const invalidData = { "not": "an array" };

    const response = await fetch(`${API_BASE_URL}/newaccount/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`
      },
      body: JSON.stringify(invalidData)
    });

    expect(response.status).toBe(400);
    const data: any = await response.json();
    expect(data.error).toBe('Invalid data format. Expected an array of accounts.');
  });

  it('should handle unauthorized request', async () => {
    const testData = [
      {
        "email": "<EMAIL>",
        "password": "testpass",
        "resetStatus": 1
      }
    ];

    const response = await fetch(`${API_BASE_URL}/newaccount/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid_token'
      },
      body: JSON.stringify(testData)
    });

    expect(response.status).toBe(401);
    const data: any = await response.json();
    expect(data.error).toBe('Unauthorized');
  });

  it('should handle method not allowed', async () => {
    const response = await fetch(`${API_BASE_URL}/newaccount/update`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`
      }
    });

    expect(response.status).toBe(405);
    const data: any = await response.json();
    expect(data.error).toBe('Method not allowed');
  });

  it('should handle OPTIONS request', async () => {
    const response = await fetch(`${API_BASE_URL}/newaccount/update`, {
      method: 'OPTIONS'
    });

    expect(response.status).toBe(204);
    expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST');
  });

  it('should update only specific fields when provided', async () => {
    const testData = [
      {
        "email": "<EMAIL>",
        "resetStatus": 2,
        "resetFailMsg": "Reset failed for testing"
      }
    ];

    const response = await fetch(`${API_BASE_URL}/newaccount/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`
      },
      body: JSON.stringify(testData)
    });

    expect(response.status).toBe(200);
    const data: any = await response.json();
    expect(data.success).toBe(true);
  });
});
