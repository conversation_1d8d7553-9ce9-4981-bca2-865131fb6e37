import { EventContext } from '@cloudflare/workers-types';
import { Env } from './types';

export const onRequest = async (_context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
   
    console.log(JSON.stringify(_context))
    return new Response(JSON.stringify({
        "status": "ok"
    }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
    });
}
