import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import moment from 'moment-timezone';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq, and } from 'drizzle-orm';

// 获取需要比较的字段（排除id）
const compareFields = Object.keys(newaccountsTable).filter(key => key !== 'id');

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    if (request.method !== 'POST') {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Method not allowed'
        }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        }));
    }

    try {
        const newAccounts = await request.json() as any[];

        if (!Array.isArray(newAccounts)) {
            return addCorsHeaders(new Response(JSON.stringify({
                error: 'Invalid data format. Expected an array of accounts.'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

        // 获取数据库中所有现有账号
        const existingAccounts = await db.select().from(newaccountsTable);

        // 处理每个提交的账号
        for (const account of newAccounts) {
            if (!account.email || !account.password) {
                continue; // 跳过无效账号
            }

            const accountData = {
                email: account.email,
                password: account.password,
                proofEmail: account.proofEmail || '',
                createDatetime: account.createDatetime || currentTime,
                resetStatus: account.resetStatus || 0,
                resetDatetime: account.resetDatetime || '',
                resetFailMsg: account.resetFailMsg || '',
                initStatus: account.initStatus || 0,
                initDatetime: account.initDatetime || '',
                initFailMsg: account.initFailMsg || ''
            };

            // 查找是否存在该账号
            const existingAccount = existingAccounts.find(ea =>
                ea.email === account.email
            );

            if (existingAccount) {
                // 检查是否需要更新
                const needUpdate = compareFields.some(key =>
                    existingAccount[key as keyof typeof existingAccount] !== accountData[key as keyof typeof accountData]
                );
                if (needUpdate) {
                    await db.update(newaccountsTable)
                        .set(accountData)
                        .where(eq(newaccountsTable.email, account.email));
                }
            } else {
                // 插入新账号
                await db.insert(newaccountsTable).values(accountData);
            }
        }

        // 删除不在新列表中的账号
        const newEmails = newAccounts.map(a => a.email);
        for (const existingAccount of existingAccounts) {
            if (!newEmails.includes(existingAccount.email)) {
                await db.delete(newaccountsTable)
                    .where(eq(newaccountsTable.email, existingAccount.email));
            }
        }

        return addCorsHeaders(new Response(JSON.stringify({
            success: true,
            message: 'New accounts saved successfully',
            timestamp: new Date().toISOString()
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};
