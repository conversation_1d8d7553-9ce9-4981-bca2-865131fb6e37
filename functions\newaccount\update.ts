import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import moment from 'moment-timezone';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    if (request.method !== 'POST') {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Method not allowed'
        }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        }));
    }

    try {
        const accounts = await request.json() as any[];

        if (!Array.isArray(accounts)) {
            return addCorsHeaders(new Response(JSON.stringify({
                error: 'Invalid data format. Expected an array of accounts.'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
        let updatedCount = 0;
        let notFoundCount = 0;
        const notFoundEmails: string[] = [];

        // 批量更新账号
        for (const account of accounts) {
            if (!account.email) {
                continue; // 跳过没有邮箱的账号
            }

            // 查找现有账号
            const existingAccount = await db.select()
                .from(newaccountsTable)
                .where(eq(newaccountsTable.email, account.email))
                .limit(1);

            if (existingAccount.length === 0) {
                notFoundCount++;
                notFoundEmails.push(account.email);
                continue;
            }

            // 准备更新数据
            const updateData: any = {
                ...(account.password !== "" && { password: account.password }),
                ...(account.proofEmail !== "" && { proofEmail: account.proofEmail }),
                ...(account.resetFailMsg !== "" && { resetFailMsg: account.resetFailMsg }),
                ...(account.initFailMsg !== "" && { initFailMsg: account.initFailMsg }),
                ...(account.resetStatus !== 0 && {
                    resetStatus: account.resetStatus,
                    resetDatetime: currentTime  //第一次的时候要设置当前时间
                }),
                ...(account.resetDatetime !== "" && { resetDatetime: account.resetDatetime }), //如果存在时间，就要用之前存的时间
                ...(account.initStatus !== 0 && {
                    initStatus: account.initStatus,
                    initDatetime: currentTime
                }),
                ...(account.initDatetime !== "" && { initDatetime: account.initDatetime }),
            };

            // 如果有数据需要更新
            if (Object.keys(updateData).length > 0) {
                await db.update(newaccountsTable)
                    .set(updateData)
                    .where(eq(newaccountsTable.email, account.email));

                updatedCount++;
            }
        }

        return addCorsHeaders(new Response(JSON.stringify({
            success: true,
            message: 'Accounts updated successfully',
            updatedCount: updatedCount,
            notFoundCount: notFoundCount,
            notFoundEmails: notFoundEmails,
            timestamp: currentTime
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};
